// Notification Types and Interfaces

export type NotificationTargetType = 
  | "single"        // Send to specific user ID
  | "all_clients"   // Send to all clients
  | "all_vendors"   // Send to all vendors
  | "group";        // Send to current group (existing functionality)

export interface NotificationPayload {
  targetType: NotificationTargetType;
  userId?: string | string[];  // Required for "single" type, optional for others
  title: string;
  message: string;
  data?: Record<string, unknown>;
}

export interface QueueMessage extends NotificationPayload {
  // This extends NotificationPayload for queue messages
}

// For backward compatibility with existing single user notifications
export interface LegacyNotificationPayload {
  userId: string | string[];
  title: string;
  message: string;
  data?: Record<string, unknown>;
}
