import amqp, { Channel } from "amqplib";
import config from "../config/config";
import { NotificationPayload, NotificationTargetType } from "../types/notification";

export class NotificationService {
  private channel!: Channel;

  constructor() {
    this.init();
  }

  async init() {
    const connection = await amqp.connect(config.msgBrokerURL!);
    this.channel = await connection.createChannel();
    await this.channel.assertQueue(config.queue.notifications);
  }

  /**
   * Send notification to a specific user
   */
  async sendToUser(
    userId: string,
    title: string,
    message: string,
    data?: Record<string, unknown>
  ): Promise<void> {
    const payload: NotificationPayload = {
      targetType: "single",
      userId,
      title,
      message,
      data,
    };

    await this.sendNotification(payload);
  }

  /**
   * Send notification to multiple specific users
   */
  async sendToUsers(
    userIds: string[],
    title: string,
    message: string,
    data?: Record<string, unknown>
  ): Promise<void> {
    const payload: NotificationPayload = {
      targetType: "single",
      userId: userIds,
      title,
      message,
      data,
    };

    await this.sendNotification(payload);
  }

  /**
   * Send notification to all clients
   */
  async sendToAllClients(
    title: string,
    message: string,
    data?: Record<string, unknown>
  ): Promise<void> {
    const payload: NotificationPayload = {
      targetType: "all_clients",
      title,
      message,
      data,
    };

    await this.sendNotification(payload);
  }

  /**
   * Send notification to all vendors
   */
  async sendToAllVendors(
    title: string,
    message: string,
    data?: Record<string, unknown>
  ): Promise<void> {
    const payload: NotificationPayload = {
      targetType: "all_vendors",
      title,
      message,
      data,
    };

    await this.sendNotification(payload);
  }

  /**
   * Send notification to a group of users (existing functionality)
   */
  async sendToGroup(
    userIds: string[],
    title: string,
    message: string,
    data?: Record<string, unknown>
  ): Promise<void> {
    const payload: NotificationPayload = {
      targetType: "group",
      userId: userIds,
      title,
      message,
      data,
    };

    await this.sendNotification(payload);
  }

  /**
   * Generic method to send notification with custom target type
   */
  async sendNotificationByType(
    targetType: NotificationTargetType,
    title: string,
    message: string,
    data?: Record<string, unknown>,
    userId?: string | string[]
  ): Promise<void> {
    const payload: NotificationPayload = {
      targetType,
      title,
      message,
      data,
      userId,
    };

    await this.sendNotification(payload);
  }

  /**
   * Private method to send notification to queue
   */
  private async sendNotification(payload: NotificationPayload): Promise<void> {
    try {
      const message = Buffer.from(JSON.stringify(payload));
      await this.channel.sendToQueue(config.queue.notifications, message, {
        persistent: true,
      });
      console.log("Notification sent to queue:", payload);
    } catch (error) {
      console.error("Error sending notification to queue:", error);
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
