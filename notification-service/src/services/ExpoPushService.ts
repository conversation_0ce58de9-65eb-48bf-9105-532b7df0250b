import {
  Expo,
  ExpoPushMessage,
  ExpoPushReceipt,
  ExpoPushTicket,
} from "expo-server-sdk";
import config from "../config/config";
import { ExpoPushToken, Notification, Client, Vendor } from "../database";
import { NotificationTargetType } from "../types/notification";

export class ExpoPushService {
  private expo: Expo;

  constructor() {
    this.expo = new Expo({ accessToken: config.EXPO_ACCESS_TOKEN });
  }

  /**
   * Save notification to database for specific users
   */
  private async saveNotificationToDatabase(
    userIds: string[],
    title: string,
    body: string,
    data?: Record<string, unknown>
  ): Promise<string[]> {
    const notificationIds: string[] = [];

    for (const userId of userIds) {
      try {
        const notification = new Notification({
          profile: userId,
          title,
          message: body,
          type: data?.type || "other",
          data: data || {},
          status: "unread",
        });

        const savedNotification = await notification.save();
        notificationIds.push(savedNotification._id?.toString() || "");
      } catch (error) {
        console.error(`Error saving notification for user ${userId}:`, error);
      }
    }

    // Save notification IDs to user models
    await this.saveNotificationIdsToUserModels(userIds, notificationIds);

    return notificationIds;
  }

  /**
   * Save notification IDs to user models (client or vendor)
   */
  private async saveNotificationIdsToUserModels(
    userIds: string[],
    notificationIds: string[]
  ): Promise<void> {
    for (let i = 0; i < userIds.length && i < notificationIds.length; i++) {
      const userId = userIds[i];
      const notificationId = notificationIds[i];

      try {
        // Try to find and update client first
        const client = await Client.findOne({ profile: userId });
        if (client) {
          await Client.findByIdAndUpdate(
            client._id,
            { $push: { notifications: notificationId } },
            { new: true }
          );
          console.log(
            `Saved notification ${notificationId} to client ${client._id}`
          );
          continue;
        }

        // If not found as client, try vendor
        const vendor = await Vendor.findOne({ profile: userId });
        if (vendor) {
          await Vendor.findByIdAndUpdate(
            vendor._id,
            { $push: { notifications: notificationId } },
            { new: true }
          );
          console.log(
            `Saved notification ${notificationId} to vendor ${vendor._id}`
          );
        } else {
          console.warn(`User not found for profile ${userId}`);
        }
      } catch (error) {
        console.error(`Error saving notification to user ${userId}:`, error);
      }
    }
  }

  async sendPushNotification(
    userId: string | string[], // Allows single token or list of tokens
    title: string,
    body: string,
    data?: Record<string, unknown>
  ): Promise<string[]> {
    const messages: ExpoPushMessage[] = [];

    // Ensure userId is always an array
    const userIds = Array.isArray(userId) ? userId : [userId];
    console.log("userIds", userIds);

    // Save notifications to database first
    const notificationIds = await this.saveNotificationToDatabase(
      userIds,
      title,
      body,
      data
    );

    const ExpoPushTokens = await ExpoPushToken.find({
      user_id: { $in: userIds },
      active: true,
      notification: true,
    });
    const tokens = ExpoPushTokens.map((token) => token.expoPushToken);
    for (const token of tokens) {
      // Validate each Expo push token
      if (!Expo.isExpoPushToken(token)) {
        console.error(`Push token ${token} is not a valid Expo push token`);
        // await PushToken.updateOne({ ExpoPushToken: token }, { active: false });
        continue;
      }

      // Construct a message for each token
      messages.push({
        to: token,
        sound: "alert.wav",
        title,
        body,
        data: data || {},
      });
    }

    if (messages.length === 0) {
      console.error("No valid push tokens found.");
      return notificationIds; // Still return notification IDs even if no push tokens
    }

    try {
      const tickets: ExpoPushTicket[] =
        await this.expo.sendPushNotificationsAsync(messages);
      console.log("Push Notification Tickets:", tickets);
      // Handle the receipts for the tickets
      await this.handleReceipts(tickets);
    } catch (error) {
      console.error("Error sending push notifications:", error);
    }

    return notificationIds;
  }

  async sendNotificationByType(
    targetType: NotificationTargetType,
    title: string,
    body: string,
    data?: Record<string, unknown>,
    userId?: string | string[]
  ): Promise<string[]> {
    const messages: ExpoPushMessage[] = [];
    let ExpoPushTokens: any[] = [];
    let targetUserIds: string[] = [];

    switch (targetType) {
      case "single":
        if (!userId) {
          console.error("userId is required for single notification type");
          return [];
        }
        targetUserIds = Array.isArray(userId) ? userId : [userId];
        console.log("Sending to specific users:", targetUserIds);
        ExpoPushTokens = await ExpoPushToken.find({
          user_id: { $in: targetUserIds },
          active: true,
          notification: true,
        });
        break;

      case "all_clients":
        console.log("Sending to all clients");
        ExpoPushTokens = await ExpoPushToken.find({
          type: "client",
          active: true,
          notification: true,
        });
        targetUserIds = ExpoPushTokens.map((token) => token.user_id.toString());
        break;

      case "all_vendors":
        console.log("Sending to all vendors");
        ExpoPushTokens = await ExpoPushToken.find({
          type: "vendor",
          active: true,
          notification: true,
        });
        targetUserIds = ExpoPushTokens.map((token) => token.user_id.toString());
        break;

      case "group":
        // For group notifications, fall back to existing logic
        if (!userId) {
          console.error("userId is required for group notification type");
          return [];
        }
        targetUserIds = Array.isArray(userId) ? userId : [userId];
        console.log("Sending to group users:", targetUserIds);
        ExpoPushTokens = await ExpoPushToken.find({
          user_id: { $in: targetUserIds },
          active: true,
          notification: true,
        });
        break;

      default:
        console.error("Invalid notification target type:", targetType);
        return [];
    }

    console.log(`Found ${ExpoPushTokens.length} push tokens for ${targetType}`);

    // Save notifications to database first
    const notificationIds = await this.saveNotificationToDatabase(
      targetUserIds,
      title,
      body,
      data
    );

    // Save notification IDs to user models
    await this.saveNotificationIdsToUserModels(targetUserIds, notificationIds);

    const tokens = ExpoPushTokens.map((token) => token.expoPushToken);
    for (const token of tokens) {
      // Validate each Expo push token
      if (!Expo.isExpoPushToken(token)) {
        console.error(`Push token ${token} is not a valid Expo push token`);
        continue;
      }

      // Construct a message for each token
      messages.push({
        to: token,
        sound: "alert.wav",
        title,
        body,
        data: data || {},
      });
    }

    if (messages.length === 0) {
      console.error("No valid push tokens found.");
      return notificationIds; // Still return notification IDs even if no push tokens
    }

    try {
      const tickets: ExpoPushTicket[] =
        await this.expo.sendPushNotificationsAsync(messages);
      console.log("Push Notification Tickets:", tickets);
      // Handle the receipts for the tickets
      await this.handleReceipts(tickets);
    } catch (error) {
      console.error("Error sending push notifications:", error);
    }

    return notificationIds;
  }

  private async handleReceipts(tickets: ExpoPushTicket[]): Promise<void> {
    const receiptIds = tickets
      .filter((ticket) => ticket.status === "error")
      .map((ticket) => ticket.details?.expoPushToken as string);

    const receiptIdChunks =
      this.expo.chunkPushNotificationReceiptIds(receiptIds);

    for (const chunk of receiptIdChunks) {
      try {
        const receipts: { [id: string]: ExpoPushReceipt } =
          await this.expo.getPushNotificationReceiptsAsync(chunk);

        for (const receiptId in receipts) {
          const { status, __debug, details } = receipts[receiptId];

          if (status === "ok") {
            continue;
          } else if (status === "error") {
            console.error(
              `There was an error sending a notification: ${details?.error}`
            );
            if (details && details.error) {
              if (details.error === "DeviceNotRegistered") {
                // Mark the token as inactive
                await ExpoPushToken.updateOne(
                  { expoPushToken: receiptId },
                  { active: false }
                );
                console.log("Token deactivated");
              }
              if (details.error === "InvalidCredentials") {
                await ExpoPushToken.updateOne(
                  { expoPushToken: receiptId },
                  { active: false }
                );
                console.log("Token deactivated");
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching receipts:", error);
      }
    }
  }
}
