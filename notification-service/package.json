{"name": "notification-service", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/server.ts", "build": "rm -rf build/ && tsc -p .", "start": "nodemon build/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@types/amqplib": "^0.10.7", "@types/express": "^5.0.0", "@types/nodemailer": "^6.4.17", "amqplib": "^0.10.8", "dotenv": "^16.4.7", "expo-server-sdk": "^3.13.0", "express": "^4.21.2", "firebase-admin": "^13.0.2", "mongoose": "^8.13.2", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "sib-api-v3-typescript": "^2.2.2", "ts-node": "^10.9.2", "twilio": "^5.4.1", "typescript": "^5.7.2"}}