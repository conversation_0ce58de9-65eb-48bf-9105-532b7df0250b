worker_processes 1;

events {
    worker_connections 1024;
}

http {
    # Rate limit zones
    limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=notification_limit:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=locasa_limit:10m rate=5r/s;

    # Upstreams for backend services
    upstream auth {
        server locasa-backend-auth-1:9081;
    }

    upstream notification {
        server locasa-backend-notification-1:9082;
    }

    upstream locasa {
        server locasa-backend-locasa-1:9093;
    }

    server {
        listen 86;
        client_max_body_size 10m;

        error_page 429 = /rate_limit.json;

        location = /rate_limit.json {
            internal;
            default_type application/json;
            return 429 '{"code":429,"message":"backend.too_many_requests"}';
        }

        location /auth/ {
            limit_req zone=auth_limit burst=5;

            proxy_pass http://auth;
            proxy_connect_timeout 5s;
            proxy_send_timeout 5s;
            proxy_read_timeout 5s;
        }

        location /notification/ {
            limit_req zone=notification_limit burst=10;

            proxy_pass http://notification;
            proxy_connect_timeout 5s;
            proxy_send_timeout 5s;
            proxy_read_timeout 5s;
        }

        location /locasa/ {
            limit_req zone=locasa_limit burst=10;

            proxy_pass http://locasa;
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
}
