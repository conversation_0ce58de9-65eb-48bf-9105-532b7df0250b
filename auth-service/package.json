{"name": "auth-service", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/server.ts", "build": "rm -rf build/ && tsc -p .", "start": "nodemon build/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@types/amqplib": "^0.10.6", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/validator": "^13.12.2", "amqplib": "^0.10.5", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.3", "morgan": "^1.10.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "twilio": "^5.4.2", "typescript": "^5.7.2", "validator": "^13.12.0"}, "devDependencies": {"@types/morgan": "^1.9.9", "@types/nodemailer": "^6.4.17"}}