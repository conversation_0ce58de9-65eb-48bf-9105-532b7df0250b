{"name": "locasa-service", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/server.ts", "build": "rm -rf build/ && tsc -p .", "start": "nodemon build/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.738.0", "@types/amqplib": "^0.10.6", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/validator": "^13.12.2", "amqplib": "^0.10.5", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typesense": "^2.0.3", "uuid": "^11.0.5"}, "devDependencies": {"@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5"}}