import { Order, Brand, Vendor } from "../database";
import { rabbitMQService } from "./RabbitMQService";

export class VendorNotificationService {
  /**
   * Helper method to get vendor's profile ID
   */
  private static async getVendorProfileId(
    vendorId: string
  ): Promise<string | null> {
    try {
      const vendor = await Vendor.findById(vendorId).populate("profile", "_id");
      return (vendor as any)?.profile?._id?.toString() || null;
    } catch (error) {
      console.error("Error fetching vendor profile ID:", error);
      return null;
    }
  }

  /**
   * Check if this is the vendor's first order of the day and send congratulations
   */
  static async checkFirstOrderOfDay(vendorId: string, orderId: string) {
    try {
      // Get today's date range
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get all vendor brands
      const vendorBrands = await Brand.find({ vendor: vendorId }).select("_id");
      const brandIds = vendorBrands.map((brand) => brand._id);

      if (brandIds.length === 0) return;

      // Count orders for today
      const todayOrdersCount = await Order.countDocuments({
        brand: { $in: brandIds },
        createdAt: { $gte: today, $lt: tomorrow },
        orderStatus: { $ne: "Cancelled" },
      });

      // If this is the first order of the day, send congratulations
      if (todayOrdersCount === 1) {
        const vendorProfileId = await this.getVendorProfileId(vendorId);
        if (vendorProfileId) {
          await rabbitMQService.notifyUser(
            vendorProfileId,
            "First Order of the Day! 🌅",
            "Congratulations! You've received your first order today. Here's to a successful day ahead!",
            {
              type: "first_order_today",
              orderId: orderId,
              date: today.toISOString(),
              action: "view_order",
              url: {
                pathname: `/(vendor)/(screens)/orders/${orderId}/order-details`,
              },
            }
          );
        }
      }
    } catch (error) {
      console.error("Error checking first order of day:", error);
    }
  }

  /**
   * Check for busy period (multiple orders in short time) and send alert
   */
  static async checkBusyPeriod(vendorId: string) {
    try {
      // Check orders in the last hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      // Get all vendor brands
      const vendorBrands = await Brand.find({ vendor: vendorId }).select("_id");
      const brandIds = vendorBrands.map((brand) => brand._id);

      if (brandIds.length === 0) return;

      // Count orders in the last hour
      const recentOrdersCount = await Order.countDocuments({
        brand: { $in: brandIds },
        createdAt: { $gte: oneHourAgo },
        orderStatus: { $ne: "Cancelled" },
      });

      // If 5 or more orders in the last hour, it's a busy period
      if (recentOrdersCount >= 5) {
        const vendorProfileId = await this.getVendorProfileId(vendorId);
        if (vendorProfileId) {
          await rabbitMQService.notifyUser(
            vendorProfileId,
            "Busy Period Alert! 🔥",
            `You're having a busy hour with ${recentOrdersCount} orders! Great job - your customers love what you're offering!`,
            {
              type: "busy_period",
              orderCount: recentOrdersCount,
              timeframe: "1 hour",
              action: "view_orders",
              url: {
                pathname: `/(vendor)/(tabs)/orders`,
              },
            }
          );
        }
      }
    } catch (error) {
      console.error("Error checking busy period:", error);
    }
  }

  /**
   * Send weekly summary notification to vendors
   */
  static async sendWeeklySummary(vendorId: string) {
    try {
      // Get last 7 days
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      // Get all vendor brands
      const vendorBrands = await Brand.find({ vendor: vendorId }).select(
        "_id name"
      );
      const brandIds = vendorBrands.map((brand) => brand._id);

      if (brandIds.length === 0) return;

      // Get weekly stats
      const weeklyOrders = await Order.find({
        brand: { $in: brandIds },
        createdAt: { $gte: weekAgo },
        orderStatus: { $ne: "Cancelled" },
      });

      const totalOrders = weeklyOrders.length;
      const totalRevenue = weeklyOrders.reduce(
        (sum, order) => sum + order.totalPrice,
        0
      );
      const deliveredOrders = weeklyOrders.filter(
        (order) => order.orderStatus === "Delivered"
      ).length;

      if (totalOrders > 0) {
        const vendorProfileId = await this.getVendorProfileId(vendorId);
        if (vendorProfileId) {
          await rabbitMQService.notifyUser(
            vendorProfileId,
            "Weekly Summary 📊",
            `This week: ${totalOrders} orders, $${totalRevenue.toFixed(
              2
            )} revenue, ${deliveredOrders} delivered. Keep up the great work!`,
            {
              type: "weekly_summary",
              totalOrders,
              totalRevenue,
              deliveredOrders,
              weekStart: weekAgo.toISOString(),
              action: "view_analytics",
              url: {
                pathname: `/(vendor)/(screens)/`,
              },
            }
          );
        }
      }
    } catch (error) {
      console.error("Error sending weekly summary:", error);
    }
  }

  /**
   * Send milestone celebration (e.g., 100th order, 50th customer)
   */
  static async checkMilestones(vendorId: string) {
    try {
      // Get all vendor brands
      const vendorBrands = await Brand.find({ vendor: vendorId }).select("_id");
      const brandIds = vendorBrands.map((brand) => brand._id);

      if (brandIds.length === 0) return;

      // Count total orders ever
      const totalOrders = await Order.countDocuments({
        brand: { $in: brandIds },
        orderStatus: { $ne: "Cancelled" },
      });

      // Check for milestone numbers
      const milestones = [10, 25, 50, 100, 250, 500, 1000];

      if (milestones.includes(totalOrders)) {
        let message = "";
        let emoji = "🎉";

        if (totalOrders === 10) {
          message =
            "You've reached your first 10 orders! This is just the beginning of your success story.";
          emoji = "🌟";
        } else if (totalOrders === 100) {
          message =
            "100 orders milestone! You're building a strong customer base.";
          emoji = "💯";
        } else if (totalOrders === 500) {
          message =
            "500 orders! You're now a well-established business on our platform.";
          emoji = "🚀";
        } else if (totalOrders === 1000) {
          message =
            "1000 orders! You're a true success story. Congratulations!";
          emoji = "👑";
        } else {
          message = `${totalOrders} orders milestone reached! Your business is growing steadily.`;
        }

        const vendorProfileId = await this.getVendorProfileId(vendorId);
        if (vendorProfileId) {
          await rabbitMQService.notifyUser(
            vendorProfileId,
            `Milestone Achieved! ${emoji}`,
            message,
            {
              type: "milestone",
              milestone: totalOrders,
              action: "view_analytics",
              url: {
                pathname: `/(vendor)/(screens)/`,
              },
            }
          );
        }
      }
    } catch (error) {
      console.error("Error checking milestones:", error);
    }
  }

  /**
   * Send notification when vendor gets their first review
   */
  static async checkFirstReview(vendorId: string, brandId: string) {
    try {
      const brand = await Brand.findById(brandId).populate("reviews");

      if (brand && brand.reviews && brand.reviews.length === 1) {
        const vendorProfileId = await this.getVendorProfileId(vendorId);
        if (vendorProfileId) {
          await rabbitMQService.notifyUser(
            vendorProfileId,
            "First Review Received! ⭐",
            `Congratulations! ${brand.name} just received its first customer review. This is a big step in building trust with customers!`,
            {
              type: "first_review",
              brandId: brandId,
              brandName: brand.name,
              action: "view_reviews",
              url: {
                pathname: `/(vendor)/(screens)/brands/${brandId}/brand-details`,
              },
            }
          );
        }
      }
    } catch (error) {
      console.error("Error checking first review:", error);
    }
  }
}
